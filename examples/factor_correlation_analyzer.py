import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sqlalchemy import create_engine
from scipy.stats import pearsonr, spearmanr
import warnings
warnings.filterwarnings('ignore')

class FactorCorrelationAnalyzer:
    """
    股票因子正相关性分析器
    专注于因子间相关性、因子收益相关性、因子稳定性分析
    """
    
    def __init__(self, engine):
        """
        初始化
        Args:
            engine: SQLAlchemy数据库引擎
        """
        self.engine = engine
        self.data_cache = {}
        self.factor_cache = {}
        
    def load_data(self, start_date=None, end_date=None, stock_codes=None):
        """
        加载基础数据
        Args:
            start_date: 开始日期 'YYYY-MM-DD'
            end_date: 结束日期 'YYYY-MM-DD'
            stock_codes: 股票代码列表
        Returns:
            dict: 包含所有字段DataFrame的字典
        """
        # 构建WHERE条件
        where_conditions = []
        params = {}
        
        if start_date:
            where_conditions.append("h.trade_date >= %(start_date)s")
            params['start_date'] = start_date
        if end_date:
            where_conditions.append("h.trade_date <= %(end_date)s")
            params['end_date'] = end_date
        if stock_codes:
            placeholders = ','.join([f"'{code}'" for code in stock_codes])
            where_conditions.append(f"h.ts_code IN ({placeholders})")
        
        where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        # 主查询SQL
        sql = f"""
        SELECT 
            h.ts_code,
            h.trade_date,
            h.open,
            h.high,
            h.low,
            h.close,
            h.pre_close,
            h.pct_chg,
            h.vol,
            h.amount,
            b.total_mv,
            b.circ_mv,
            b.turnover_rate,
            b.pe_ttm,
            b.pb,
            s.industry,
            s.area
        FROM stock_daily_history h
        LEFT JOIN stock_daily_basic b ON h.ts_code = b.ts_code AND h.trade_date = b.trade_date
        LEFT JOIN stock_basic s ON h.ts_code = s.ts_code
        {where_clause}
        ORDER BY h.trade_date, h.ts_code
        """
        
        print("正在加载数据...")
        df = pd.read_sql(sql, self.engine, params=params)
        print(f"加载完成，共{len(df)}条记录")
        
        # 数据透视
        data = {}
        for col in ['open', 'high', 'low', 'close', 'pre_close', 'vol', 'amount', 'total_mv', 'circ_mv', 'turnover_rate', 'pe_ttm', 'pb', 'pct_chg']:
            data[col] = df.pivot(index='trade_date', columns='ts_code', values=col)
        
        # 计算衍生指标
        data['returns'] = data['pct_chg'] / 100
        data['volume'] = data['vol'] * 100
        data['vwap'] = (data['amount'] * 1000) / (data['vol'] * 100)
        data['cap'] = data['total_mv'] * 10000
        
        # 行业映射
        industry_df = df[['ts_code', 'industry']].drop_duplicates()
        data['industry_map'] = dict(zip(industry_df['ts_code'], industry_df['industry']))
        
        self.data_cache = data
        return data
    
    # ======================基础工具函数======================
    
    @staticmethod
    def _ensure_dataframe(df):
        """确保输入是DataFrame格式"""
        if isinstance(df, np.ndarray):
            return pd.DataFrame(df)
        elif isinstance(df, pd.Series):
            return df.to_frame()
        return df
    
    @staticmethod
    def rank(df):
        """横截面排名"""
        try:
            df = FactorCorrelationAnalyzer._ensure_dataframe(df)
            return df.rank(axis=1, pct=True)
        except Exception as e:
            print(f"rank函数错误: {e}")
            return df
    
    @staticmethod
    def ts_rank(df, window):
        """时间序列排名"""
        try:
            df = FactorCorrelationAnalyzer._ensure_dataframe(df)
            return df.rolling(int(window)).rank(pct=True)
        except Exception as e:
            print(f"ts_rank函数错误: {e}")
            return df
    
    @staticmethod
    def ts_corr(x, y, window):
        """时间序列相关系数"""
        try:
            x = FactorCorrelationAnalyzer._ensure_dataframe(x)
            y = FactorCorrelationAnalyzer._ensure_dataframe(y)
            return x.rolling(int(window)).corr(y)
        except Exception as e:
            print(f"ts_corr函数错误: {e}")
            return x
    
    @staticmethod
    def ts_mean(df, window):
        """时间序列均值"""
        try:
            df = FactorCorrelationAnalyzer._ensure_dataframe(df)
            return df.rolling(int(window)).mean()
        except Exception as e:
            print(f"ts_mean函数错误: {e}")
            return df
    
    @staticmethod
    def ts_stddev(df, window):
        """时间序列标准差"""
        try:
            df = FactorCorrelationAnalyzer._ensure_dataframe(df)
            return df.rolling(int(window)).std()
        except Exception as e:
            print(f"ts_stddev函数错误: {e}")
            return df
    
    @staticmethod
    def delay(df, period):
        """滞后"""
        try:
            df = FactorCorrelationAnalyzer._ensure_dataframe(df)
            return df.shift(int(period))
        except Exception as e:
            print(f"delay函数错误: {e}")
            return df
    
    @staticmethod
    def delta(df, period):
        """差分"""
        try:
            df = FactorCorrelationAnalyzer._ensure_dataframe(df)
            return df.diff(int(period))
        except Exception as e:
            print(f"delta函数错误: {e}")
            return df
    
    @staticmethod
    def ts_sum(df, window):
        """时间序列求和"""
        try:
            df = FactorCorrelationAnalyzer._ensure_dataframe(df)
            return df.rolling(int(window)).sum()
        except Exception as e:
            print(f"ts_sum函数错误: {e}")
            return df
    
    @staticmethod
    def ts_min(df, window):
        """时间序列最小值"""
        try:
            df = FactorCorrelationAnalyzer._ensure_dataframe(df)
            return df.rolling(int(window)).min()
        except Exception as e:
            print(f"ts_min函数错误: {e}")
            return df
    
    @staticmethod
    def ts_max(df, window):
        """时间序列最大值"""
        try:
            df = FactorCorrelationAnalyzer._ensure_dataframe(df)
            return df.rolling(int(window)).max()
        except Exception as e:
            print(f"ts_max函数错误: {e}")
            return df
    
    @staticmethod
    def ts_argmax(df, window):
        """时间序列最大值索引"""
        try:
            df = FactorCorrelationAnalyzer._ensure_dataframe(df)
            return df.rolling(int(window)).apply(lambda x: x.argmax(), raw=False)
        except Exception as e:
            print(f"ts_argmax函数错误: {e}")
            return df
    
    @staticmethod
    def ts_argmin(df, window):
        """时间序列最小值索引"""
        try:
            df = FactorCorrelationAnalyzer._ensure_dataframe(df)
            return df.rolling(int(window)).apply(lambda x: x.argmin(), raw=False)
        except Exception as e:
            print(f"ts_argmin函数错误: {e}")
            return df
    
    @staticmethod
    def ts_cov(x, y, window):
        """时间序列协方差"""
        try:
            x = FactorCorrelationAnalyzer._ensure_dataframe(x)
            y = FactorCorrelationAnalyzer._ensure_dataframe(y)
            return x.rolling(int(window)).cov(y)
        except Exception as e:
            print(f"ts_cov函数错误: {e}")
            return x
    
    @staticmethod
    def scale(df):
        """标准化（去均值除以标准差）"""
        try:
            df = FactorCorrelationAnalyzer._ensure_dataframe(df)
            return (df - df.mean()) / df.std()
        except Exception as e:
            print(f"scale函数错误: {e}")
            return df
    
    @staticmethod
    def decay_linear(df, period):
        """线性衰减权重"""
        try:
            df = FactorCorrelationAnalyzer._ensure_dataframe(df)
            def linear_decay(x):
                if len(x) == 0:
                    return np.nan
                weights = np.arange(1, len(x) + 1)
                return np.average(x, weights=weights)
            
            return df.rolling(int(period)).apply(linear_decay, raw=False)
        except Exception as e:
            print(f"decay_linear函数错误: {e}")
            return df
    
    @staticmethod
    def product(df, window):
        """滚动乘积"""
        try:
            df = FactorCorrelationAnalyzer._ensure_dataframe(df)
            return df.rolling(int(window)).apply(lambda x: np.prod(x), raw=False)
        except Exception as e:
            print(f"product函数错误: {e}")
            return df
    
    @staticmethod
    def signed_power(base, exponent):
        """带符号的幂运算"""
        try:
            base = FactorCorrelationAnalyzer._ensure_dataframe(base)
            exponent = FactorCorrelationAnalyzer._ensure_dataframe(exponent)
            return np.sign(base) * (np.abs(base) ** exponent)
        except Exception as e:
            print(f"signed_power函数错误: {e}")
            return base
    
    # ======================示例因子（简化版）======================
    
    def momentum_factor(self, data, window=20):
        """动量因子"""
        returns = data['returns']
        return self.ts_mean(returns, window)
    
    def volatility_factor(self, data, window=20):
        """波动率因子"""
        returns = data['returns']
        return self.ts_stddev(returns, window)
    
    def turnover_factor(self, data):
        """换手率因子"""
        return self.rank(data['turnover_rate'])
    
    def size_factor(self, data):
        """规模因子"""
        return self.rank(np.log(data['cap']))
    
    def value_factor(self, data):
        """价值因子（PB倒数）"""
        return self.rank(1 / data['pb'])

    # ======================选择性Alpha因子实现======================
    
    def alpha001(self, data):
        """
        Alpha001: rank(ts_argmax(power(((returns < 0) ? ts_stddev(returns, 20) : close), 2), 5)) - 0.5
        """
        close, returns = data['close'], data['returns']
        condition = returns < 0
        values = np.where(condition, self.ts_stddev(returns, 20), close)
        return self.rank(self.ts_argmax(values ** 2, 5)) - 0.5
    
    def alpha002(self, data):
        """
        Alpha002: (-1 * ts_corr(rank(delta(log(volume), 2)), rank(((close - open) / open)), 6))
        """
        close, open_price, volume = data['close'], data['open'], data['volume']
        return -1 * self.ts_corr(
            self.rank(self.delta(np.log(volume), 2)),
            self.rank((close - open_price) / open_price),
            6
        )
    
    def alpha003(self, data):
        """
        Alpha003: (-1 * ts_corr(rank(open), rank(volume), 10))
        """
        open_price, volume = data['open'], data['volume']
        return -1 * self.ts_corr(self.rank(open_price), self.rank(volume), 10)
    
    def alpha004(self, data):
        """
        Alpha004: (-1 * ts_rank(rank(low), 9))
        """
        low = data['low']
        return -1 * self.ts_rank(self.rank(low), 9)
    
    def alpha005(self, data):
        """
        Alpha005: (rank((open - (ts_sum(vwap, 10) / 10))) * (-1 * abs(rank((close - vwap)))))
        """
        open_price, close, vwap = data['open'], data['close'], data['vwap']
        return (self.rank(open_price - self.ts_mean(vwap, 10)) * 
                (-1 * np.abs(self.rank(close - vwap))))
    
    def alpha006(self, open, volume):
        """
        Alpha006: (-1 * ts_corr(open, volume, 10))
        """
        return -1 * self.ts_corr(open, volume, 10)
    
    def alpha007(self, close, volume, adv20):
        """
        Alpha007: ((adv20 < volume) ? ((-1 * ts_rank(abs(delta(close, 7)), 60)) * sign(delta(close, 7))) : (-1))
        """
        condition = adv20 < volume
        alpha = -1 * self.ts_rank(np.abs(self.delta(close, 7)), 60) * np.sign(self.delta(close, 7))
        return np.where(condition, alpha, -1)
    
    def alpha008(self, open, returns):
        """
        Alpha008: (-1 * rank(((ts_sum(open, 5) * ts_sum(returns, 5)) - 
                   delay((ts_sum(open, 5) * ts_sum(returns, 5)), 10))))
        """
        inner = self.ts_sum(open, 5) * self.ts_sum(returns, 5)
        return -1 * self.rank(inner - self.delay(inner, 10))
    
    def alpha009(self, close):
        """
        Alpha009: ((0 < ts_min(delta(close, 1), 5)) ? delta(close, 1) : 
                  ((ts_max(delta(close, 1), 5) < 0) ? delta(close, 1) : (-1 * delta(close, 1))))
        """
        delta_close = self.delta(close, 1)
        condition1 = self.ts_min(delta_close, 5) > 0
        condition2 = self.ts_max(delta_close, 5) < 0
        
        result = np.where(condition1, delta_close, 
                         np.where(condition2, delta_close, -1 * delta_close))
        return result
    
    def alpha010(self, close):
        """
        Alpha010: rank(((0 < ts_min(delta(close, 1), 4)) ? delta(close, 1) : 
                       ((ts_max(delta(close, 1), 4) < 0) ? delta(close, 1) : (-1 * delta(close, 1)))))
        """
        delta_close = self.delta(close, 1)
        condition1 = self.ts_min(delta_close, 4) > 0
        condition2 = self.ts_max(delta_close, 4) < 0
        
        result = np.where(condition1, delta_close, 
                         np.where(condition2, delta_close, -1 * delta_close))
        return self.rank(result)
    
    def alpha011(self, close, vwap, volume):
        """
        Alpha011: ((rank(ts_max((vwap - close), 3)) + rank(ts_min((vwap - close), 3))) * 
                   rank(delta(volume, 3)))
        """
        vwap_close = vwap - close
        return ((self.rank(self.ts_max(vwap_close, 3)) + 
                self.rank(self.ts_min(vwap_close, 3))) * 
                self.rank(self.delta(volume, 3)))
    
    def alpha012(self, close, volume):
        """
        Alpha012: (sign(delta(volume, 1)) * (-1 * delta(close, 1)))
        """
        return np.sign(self.delta(volume, 1)) * (-1 * self.delta(close, 1))
    
    def alpha013(self, close, volume):
        """
        Alpha013: (-1 * rank(ts_cov(rank(close), rank(volume), 5)))
        """
        return -1 * self.rank(self.ts_cov(self.rank(close), self.rank(volume), 5))
    
    def alpha014(self, open, volume, returns):
        """
        Alpha014: ((-1 * rank(delta(returns, 3))) * ts_corr(open, volume, 10))
        """
        return (-1 * self.rank(self.delta(returns, 3))) * self.ts_corr(open, volume, 10)
    
    def alpha015(self, high, volume):
        """
        Alpha015: (-1 * ts_sum(rank(ts_corr(rank(high), rank(volume), 3)), 3))
        """
        return -1 * self.ts_sum(self.rank(self.ts_corr(self.rank(high), self.rank(volume), 3)), 3)
    
    def alpha016(self, high, volume):
        """
        Alpha016: (-1 * rank(ts_cov(rank(high), rank(volume), 5)))
        """
        return -1 * self.rank(self.ts_cov(self.rank(high), self.rank(volume), 5))
    
    def alpha017(self, close, volume, adv20):
        """
        Alpha017: (((-1 * rank(ts_rank(close, 10))) * rank(delta(delta(close, 1), 1))) * 
                   rank(ts_rank((volume / adv20), 5)))
        """
        return ((-1 * self.rank(self.ts_rank(close, 10))) * 
                self.rank(self.delta(self.delta(close, 1), 1)) * 
                self.rank(self.ts_rank(volume / adv20, 5)))
    
    def alpha018(self, close, open):
        """
        Alpha018: (-1 * rank(((ts_stddev(abs((close - open)), 5) + (close - open)) + 
                   ts_corr(close, open, 10))))
        """
        return -1 * self.rank((self.ts_stddev(np.abs(close - open), 5) + 
                              (close - open) + 
                              self.ts_corr(close, open, 10)))
    
    def alpha019(self, close, returns):
        """
        Alpha019: ((-1 * sign(((close - delay(close, 7)) + delta(close, 7)))) * 
                   (1 + rank((1 + ts_sum(returns, 250)))))
        """
        return (-1 * np.sign((close - self.delay(close, 7)) + self.delta(close, 7))) * \
               (1 + self.rank(1 + self.ts_sum(returns, 250)))
    
    def alpha020(self, open, high, close, low):
        """
        Alpha020: (((-1 * rank((open - delay(high, 1)))) * rank((open - delay(close, 1)))) * 
                   rank((open - delay(low, 1))))
        """
        return ((-1 * self.rank(open - self.delay(high, 1))) * 
                self.rank(open - self.delay(close, 1)) * 
                self.rank(open - self.delay(low, 1)))
    
    def alpha021(self, close, volume, adv20):
        """
        Alpha021: 复杂条件判断
        """
        mean_8 = self.ts_mean(close, 8)
        std_8 = self.ts_stddev(close, 8)
        mean_2 = self.ts_mean(close, 2)
        
        condition1 = (mean_8 + std_8) < mean_2
        condition2 = mean_2 < (mean_8 - std_8)
        condition3 = (volume / adv20) >= 1
        
        return np.where(condition1, -1, 
                       np.where(condition2, 1, 
                               np.where(condition3, 1, -1)))
    
    def alpha022(self, high, volume, close):
        """
        Alpha022: (-1 * (delta(ts_corr(high, volume, 5), 5) * rank(ts_stddev(close, 20))))
        """
        return -1 * (self.delta(self.ts_corr(high, volume, 5), 5) * 
                     self.rank(self.ts_stddev(close, 20)))
    
    def alpha023(self, high, close):
        """
        Alpha023: (((ts_sum(high, 20) / 20) < high) ? (-1 * delta(high, 2)) : 0)
        """
        condition = self.ts_mean(high, 20) < high
        return np.where(condition, -1 * self.delta(high, 2), 0)
    
    def alpha024(self, close):
        """
        Alpha024: 复杂条件判断
        """
        mean_100 = self.ts_mean(close, 100)
        condition = (self.delta(mean_100, 100) / self.delay(close, 100)) <= 0.05
        return np.where(condition, 
                       -1 * (close - self.ts_min(close, 100)),
                       -1 * self.delta(close, 3))
    
    def alpha025(self, close, returns, adv20, vwap, high):
        """
        Alpha025: rank(((((-1 * returns) * adv20) * vwap) * (high - close)))
        """
        return self.rank((-1 * returns) * adv20 * vwap * (high - close))
    
    def alpha026(self, volume, high):
        """
        Alpha026: (-1 * ts_max(ts_corr(ts_rank(volume, 5), ts_rank(high, 5), 5), 3))
        """
        return -1 * self.ts_max(self.ts_corr(self.ts_rank(volume, 5), 
                                            self.ts_rank(high, 5), 5), 3)
    
    def alpha027(self, volume, vwap):
        """
        Alpha027: ((0.5 < rank((ts_sum(ts_corr(rank(volume), rank(vwap), 6), 2) / 2.0))) ? (-1) : 1)
        """
        inner = self.ts_sum(self.ts_corr(self.rank(volume), self.rank(vwap), 6), 2) / 2.0
        condition = self.rank(inner) > 0.5
        return np.where(condition, -1, 1)
    
    def alpha028(self, adv20, low, high, close):
        """
        Alpha028: scale(((ts_corr(adv20, low, 5) + ((high + low) / 2)) - close))
        """
        return self.scale(self.ts_corr(adv20, low, 5) + ((high + low) / 2) - close)
    
    def alpha029(self, close, returns):
        """
        Alpha029: 复杂嵌套计算
        """
        inner = self.rank(self.rank(-1 * self.rank(self.delta(close - 1, 5))))
        inner = self.rank(self.rank(self.scale(np.log(self.ts_sum(self.ts_min(inner, 2), 1)))))
        inner = self.rank(self.product(inner, 1))
        part1 = self.ts_min(inner, 5)
        part2 = self.ts_rank(self.delay(-1 * returns, 6), 5)
        return part1 + part2
    
    def alpha030(self, close, volume):
        """
        Alpha030: 复杂信号判断
        """
        sign_sum = (np.sign(self.delta(close, 1)) + 
                   np.sign(self.delta(close, 2)) + 
                   np.sign(self.delta(close, 3)))
        return ((1.0 - self.rank(sign_sum)) * self.ts_sum(volume, 5) / 
                self.ts_sum(volume, 20))
    
    def alpha031(self, close, adv20, low):
        """
        Alpha031: 复杂线性衰减
        """
        part1 = self.rank(self.rank(self.rank(self.decay_linear(-1 * self.rank(self.rank(self.delta(close, 10))), 10))))
        part2 = self.rank(-1 * self.delta(close, 3))
        part3 = np.sign(self.scale(self.ts_corr(adv20, low, 12)))
        return part1 + part2 + part3
    
    def alpha032(self, close, vwap):
        """
        Alpha032: (scale(((ts_sum(close, 7) / 7) - close)) + 
                   (20 * scale(ts_corr(vwap, delay(close, 5), 230))))
        """
        part1 = self.scale(self.ts_mean(close, 7) - close)
        part2 = 20 * self.scale(self.ts_corr(vwap, self.delay(close, 5), 230))
        return part1 + part2
    
    def alpha033(self, open, close):
        """
        Alpha033: rank((-1 * ((1 - (open / close))^1)))
        """
        return self.rank(-1 * (1 - (open / close)))
    
    def alpha034(self, close, returns):
        """
        Alpha034: rank(((1 - rank((ts_stddev(returns, 2) / ts_stddev(returns, 5)))) + 
                       (1 - rank(delta(close, 1)))))
        """
        part1 = 1 - self.rank(self.ts_stddev(returns, 2) / self.ts_stddev(returns, 5))
        part2 = 1 - self.rank(self.delta(close, 1))
        return self.rank(part1 + part2)
    
    def alpha035(self, close, high, low, volume, returns):
        """
        Alpha035: ((ts_rank(volume, 32) * (1 - ts_rank(((close + high) - low), 16))) * 
                   (1 - ts_rank(returns, 32)))
        """
        return (self.ts_rank(volume, 32) * 
                (1 - self.ts_rank((close + high) - low, 16)) * 
                (1 - self.ts_rank(returns, 32)))
    
    def alpha036(self, close, open, volume, returns, vwap, adv20):
        """
        Alpha036: 加权组合多个因子
        """
        part1 = 2.21 * self.rank(self.ts_corr(close - open, self.delay(volume, 1), 15))
        part2 = 0.7 * self.rank(open - close)
        part3 = 0.73 * self.rank(self.ts_rank(self.delay(-1 * returns, 6), 5))
        part4 = self.rank(np.abs(self.ts_corr(vwap, adv20, 6)))
        part5 = 0.6 * self.rank(((self.ts_mean(close, 200) - open) * (close - open)))
        return part1 + part2 + part3 + part4 + part5
    
    def alpha037(self, open, close):
        """
        Alpha037: (rank(ts_corr(delay((open - close), 1), close, 200)) + rank((open - close)))
        """
        return (self.rank(self.ts_corr(self.delay(open - close, 1), close, 200)) + 
                self.rank(open - close))
    
    def alpha038(self, open, close):
        """
        Alpha038: ((-1 * rank(ts_rank(close, 10))) * rank((close / open)))
        """
        return (-1 * self.rank(self.ts_rank(close, 10))) * self.rank(close / open)
    
    def alpha039(self, close, volume, adv20, returns):
        """
        Alpha039: ((-1 * rank((delta(close, 7) * (1 - rank(decay_linear((volume / adv20), 9)))))) * 
                   (1 + rank(ts_sum(returns, 250))))
        """
        part1 = -1 * self.rank(self.delta(close, 7) * (1 - self.rank(self.decay_linear(volume / adv20, 9))))
        part2 = 1 + self.rank(self.ts_sum(returns, 250))
        return part1 * part2
    
    def alpha040(self, high, volume):
        """
        Alpha040: ((-1 * rank(ts_stddev(high, 10))) * ts_corr(high, volume, 10))
        """
        return (-1 * self.rank(self.ts_stddev(high, 10))) * self.ts_corr(high, volume, 10)
    
    def alpha041(self, high, low, vwap):
        """
        Alpha041: (((high * low)^0.5) - vwap)
        """
        return np.sqrt(high * low) - vwap
    
    def alpha042(self, close, vwap):
        """
        Alpha042: (rank((vwap - close)) / rank((vwap + close)))
        """
        return self.rank(vwap - close) / self.rank(vwap + close)
    
    def alpha043(self, close, volume, adv20):
        """
        Alpha043: (ts_rank((volume / adv20), 20) * ts_rank((-1 * delta(close, 7)), 8))
        """
        return (self.ts_rank(volume / adv20, 20) * 
                self.ts_rank(-1 * self.delta(close, 7), 8))
    
    def alpha044(self, high, volume):
        """
        Alpha044: (-1 * ts_corr(high, rank(volume), 5))
        """
        return -1 * self.ts_corr(high, self.rank(volume), 5)
    
    def alpha045(self, close, volume):
        """
        Alpha045: 复杂相关性组合
        """
        part1 = self.rank(self.ts_mean(self.delay(close, 5), 20))
        part2 = self.ts_corr(close, volume, 2)
        part3 = self.rank(self.ts_corr(self.ts_sum(close, 5), self.ts_sum(close, 20), 2))
        return -1 * part1 * part2 * part3
    
    def alpha046(self, close):
        """
        Alpha046: 均值回归条件判断
        """
        inner = ((self.delay(close, 20) - self.delay(close, 10)) / 10) - \
                ((self.delay(close, 10) - close) / 10)
        condition1 = inner > 0.25
        condition2 = inner < 0
        return np.where(condition1, -1, 
                       np.where(condition2, 1, -1 * self.delta(close, 1)))
    
    def alpha047(self, close, volume, adv20, high, vwap):
        """
        Alpha047: 复杂综合因子
        """
        part1 = (self.rank(1 / close) * volume) / adv20
        part2 = (high * self.rank(high - close)) / self.ts_mean(high, 5)
        part3 = self.rank(vwap - self.delay(vwap, 5))
        return part1 * part2 - part3
    
    def alpha048(self, close, volume):
        """
        Alpha048: 行业中性化相关性因子（简化版）
        """
        numerator = self.ts_corr(self.delta(close, 1), self.delta(self.delay(close, 1), 1), 250) * self.delta(close, 1)
        denominator = self.ts_sum((self.delta(close, 1) / self.delay(close, 1)) ** 2, 250)
        return numerator / denominator / close
    
    def alpha049(self, close):
        """
        Alpha049: 价格趋势条件判断
        """
        inner = ((self.delay(close, 20) - self.delay(close, 10)) / 10) - \
                ((self.delay(close, 10) - close) / 10)
        condition = inner < -0.1
        return np.where(condition, 1, -1 * self.delta(close, 1))
    
    def alpha050(self, volume, vwap):
        """
        Alpha050: (-1 * ts_max(rank(ts_corr(rank(volume), rank(vwap), 5)), 5))
        """
        return -1 * self.ts_max(self.rank(self.ts_corr(self.rank(volume), self.rank(vwap), 5)), 5)
    
    def alpha051(self, close):
        """
        Alpha051: 价格趋势条件判断（阈值不同）
        """
        inner = ((self.delay(close, 20) - self.delay(close, 10)) / 10) - \
                ((self.delay(close, 10) - close) / 10)
        condition = inner < -0.05
        return np.where(condition, 1, -1 * self.delta(close, 1))
    
    def alpha052(self, low, returns, volume):
        """
        Alpha052: 低价变化与收益率组合
        """
        part1 = (-1 * self.ts_min(low, 5) + self.delay(self.ts_min(low, 5), 5))
        part2 = self.rank((self.ts_sum(returns, 240) - self.ts_sum(returns, 20)) / 220)
        part3 = self.ts_rank(volume, 5)
        return part1 * part2 * part3
    
    def alpha053(self, close, high, low):
        """
        Alpha053: (-1 * delta((((close - low) - (high - close)) / (close - low)), 9))
        """
        inner = ((close - low) - (high - close)) / (close - low)
        return -1 * self.delta(inner, 9)
    
    def alpha054(self, open, close, high, low):
        """
        Alpha054: ((-1 * ((low - close) * (open^5))) / ((low - high) * (close^5)))
        """
        numerator = -1 * (low - close) * (open ** 5)
        denominator = (low - high) * (close ** 5)
        return numerator / denominator
    
    def alpha055(self, close, low, high, volume):
        """
        Alpha055: 价格相对位置与成交量相关性
        """
        price_rank = self.rank((close - self.ts_min(low, 12)) / (self.ts_max(high, 12) - self.ts_min(low, 12)))
        volume_rank = self.rank(volume)
        return -1 * self.ts_corr(price_rank, volume_rank, 6)
    
    def alpha056(self, open, high, low, adv20):
        """
        Alpha056: 开盘价排名与复杂成交量相关性比较
        """
        left = self.rank(open - self.ts_min(open, 12))
        right = self.rank(self.ts_corr(self.ts_sum((high + low) / 2, 19), 
                                     self.ts_sum(adv20, 19), 13) ** 5)
        return left < right
    
    def alpha057(self, close, vwap):
        """
        Alpha057: (0 - (1 * ((close - vwap) / decay_linear(rank(ts_argmax(close, 30)), 2))))
        """
        denominator = self.decay_linear(self.rank(self.ts_argmax(close, 30)), 2)
        return -1 * (close - vwap) / denominator
    
    def alpha058(self, vwap, volume):
        """
        Alpha058: 行业中性化VWAP与成交量相关性（简化版）
        """
        return -1 * self.ts_rank(self.decay_linear(self.ts_corr(vwap, volume, 4), 8), 6)
    
    def alpha059(self, vwap, volume):
        """
        Alpha059: 行业中性化VWAP复杂衰减排名（简化版）
        """
        weighted_vwap = vwap * 0.728317 + vwap * (1 - 0.728317)
        return -1 * self.ts_rank(self.decay_linear(self.ts_corr(weighted_vwap, volume, 4), 16), 8)

    def alpha060(self, close, volume):
        """
        Alpha060: (0 - (1 * ((2 * scale(rank(((((close - low) - (high - close)) / (high - low)) * volume)))) - scale(rank(ts_argmax(close, 10))))))
        """
        # 需要high, low数据，这里简化处理
        # 简化版本：基于close和volume的组合
        part1 = 2 * self.scale(self.rank(close * volume))
        part2 = self.scale(self.rank(self.ts_argmax(close, 10)))
        return -1 * (part1 - part2)
    
    def alpha061(self, volume, adv20, vwap, high, low):
        """
        Alpha061: (rank((vwap - ts_min(vwap, 16))) < rank(ts_corr(vwap, adv20, 18)))
        """
        left = self.rank(vwap - self.ts_min(vwap, 16))
        right = self.rank(self.ts_corr(vwap, adv20, 18))
        return left < right
    
    def alpha062(self, open, high, low, volume, adv20):
        """
        Alpha062: ((rank(ts_corr(vwap, ts_sum(adv20, 22), 10)) < rank(((rank(open) + rank(open)) < (rank(((high + low) / 2)) + rank(high))))) * -1)
        """
        # 需要vwap，这里简化处理
        vwap_approx = (high + low + open) / 3  # 简化的vwap近似
        left = self.rank(self.ts_corr(vwap_approx, self.ts_sum(adv20, 22), 10))
        right = self.rank((self.rank(open) + self.rank(open)) < (self.rank((high + low) / 2) + self.rank(high)))
        return (left < right) * -1
    
    def alpha063(self, adv20, open, close):
        """
        Alpha063: ((rank(decay_linear(delta(IndNeutralize(close, IndClass.industry), 2), 10)) - rank(decay_linear(((((delta(open, 1) * 0.25) + (delta(close, 1) * 0.75)) < -0.4) * -1), 10))) * -1)
        """
        # 简化版本，忽略行业中性化
        part1 = self.rank(self.decay_linear(self.delta(close, 2), 10))
        condition = (self.delta(open, 1) * 0.25 + self.delta(close, 1) * 0.75) < -0.4
        part2 = self.rank(self.decay_linear(condition * -1, 10))
        return (part1 - part2) * -1
    
    def alpha064(self, open, high, low, volume, adv20):
        """
        Alpha064: ((rank(ts_corr(ts_sum(((open * 0.178404) + (low * (1 - 0.178404))), 13), ts_sum(adv20, 13), 17)) < rank(delta(((((high + low) / 2) * 0.178404) + (vwap * (1 - 0.178404))), 3))) * -1)
        """
        # 简化版本
        vwap_approx = (high + low + open) / 3
        left_sum = self.ts_sum(open * 0.178404 + low * (1 - 0.178404), 13)
        left = self.rank(self.ts_corr(left_sum, self.ts_sum(adv20, 13), 17))
        right = self.rank(self.delta(((high + low) / 2) * 0.178404 + vwap_approx * (1 - 0.178404), 3))
        return (left < right) * -1
    
    def alpha065(self, open, close, adv20):
        """
        Alpha065: ((rank(ts_corr(((open * 0.00817205) + (vwap * (1 - 0.00817205))), ts_sum(adv20, 5), 8)) < rank((open - ts_min(open, 13)))) * -1)
        """
        # 简化版本，用close近似vwap
        left = self.rank(self.ts_corr(open * 0.00817205 + close * (1 - 0.00817205), 
                                     self.ts_sum(adv20, 5), 8))
        right = self.rank(open - self.ts_min(open, 13))
        return (left < right) * -1
    
    def alpha066(self, close, open, high, low, volume):
        """
        Alpha066: ((rank(decay_linear(delta(vwap, 4), 7)) + rank(decay_linear(((((low * 0.96633) + (low * (1 - 0.96633))) - vwap) / (open - ((high + low) / 2))), 11))) * -1)
        """
        # 简化版本
        vwap_approx = (high + low + open) / 3
        part1 = self.rank(self.decay_linear(self.delta(vwap_approx, 4), 7))
        numerator = low - vwap_approx
        denominator = open - (high + low) / 2
        part2 = self.rank(self.decay_linear(numerator / denominator, 11))
        return (part1 + part2) * -1
    
    def alpha067(self, high, low, open, volume):
        """
        Alpha067: ((rank((high - ts_min(high, 2))) + rank(ts_corr(IndNeutralize(vwap, IndClass.industry), IndNeutralize(volume, IndClass.industry), 7))) * -1)
        """
        # 简化版本，忽略行业中性化
        vwap_approx = (high + low + open) / 3
        part1 = self.rank(high - self.ts_min(high, 2))
        part2 = self.rank(self.ts_corr(vwap_approx, volume, 7))
        return (part1 + part2) * -1
    
    def alpha068(self, high, low, open, volume, adv15):
        """
        Alpha068: ((ts_rank(ts_corr(rank(high), rank(adv15), 9), 14) < rank(delta(((close * 0.518371) + (low * (1 - 0.518371))), 1))) * -1)
        """
        # 需要close，这里用high近似
        close_approx = (high + low) / 2
        left = self.ts_rank(self.ts_corr(self.rank(high), self.rank(adv15), 9), 14)
        right = self.rank(self.delta(close_approx * 0.518371 + low * (1 - 0.518371), 1))
        return (left < right) * -1
    
    def alpha069(self, open, high, low, volume):
        """
        Alpha069: ((rank(ts_max(delta(IndNeutralize(vwap, IndClass.industry), 3), 5)) + rank(ts_corr(((close * 0.490655) + (vwap * (1 - 0.490655))), adv20, 26))) * -1)
        """
        # 简化版本
        vwap_approx = (high + low + open) / 3
        close_approx = (high + low) / 2
        adv20_approx = self.ts_mean(volume, 20)
        
        part1 = self.rank(self.ts_max(self.delta(vwap_approx, 3), 5))
        part2 = self.rank(self.ts_corr(close_approx * 0.490655 + vwap_approx * (1 - 0.490655), 
                                      adv20_approx, 26))
        return (part1 + part2) * -1
    
    def alpha070(self, open, high, low, volume):
        """
        Alpha070: ((rank(delta(vwap, 5)) + rank(ts_corr(((close * 0.490655) + (vwap * (1 - 0.490655))), adv20, 26))) * -1)
        """
        # 简化版本
        vwap_approx = (high + low + open) / 3
        close_approx = (high + low) / 2
        adv20_approx = self.ts_mean(volume, 20)
        
        part1 = self.rank(self.delta(vwap_approx, 5))
        part2 = self.rank(self.ts_corr(close_approx * 0.490655 + vwap_approx * (1 - 0.490655), 
                                      adv20_approx, 26))
        return (part1 + part2) * -1
    
    def alpha071(self, close, open, high, low, volume, adv180):
        """
        Alpha071: max(ts_rank(decay_linear(ts_corr(ts_rank(close, 4), ts_rank(adv180, 18), 18), 16), ts_rank(decay_linear((rank(((low + open) - (vwap + vwap)))^2), 16), 13))
        """
        # 简化版本
        vwap_approx = (high + low + open) / 3
        
        part1 = self.ts_rank(self.decay_linear(
            self.ts_corr(self.ts_rank(close, 4), self.ts_rank(adv180, 18), 18), 16), 13)
        part2 = self.ts_rank(self.decay_linear(
            self.rank(((low + open) - (vwap_approx + vwap_approx)) ** 2), 16), 13)
        return np.maximum(part1, part2)
    
    def alpha072(self, high, low, open, volume, adv15):
        """
        Alpha072: (rank(decay_linear(ts_corr(((high + low) / 2), adv15, 9), 10)) / rank(decay_linear(ts_corr(ts_rank(vwap, 4), ts_rank(volume, 19), 7), 4)))
        """
        # 简化版本
        vwap_approx = (high + low + open) / 3
        
        numerator = self.rank(self.decay_linear(
            self.ts_corr((high + low) / 2, adv15, 9), 10))
        denominator = self.rank(self.decay_linear(
            self.ts_corr(self.ts_rank(vwap_approx, 4), self.ts_rank(volume, 19), 7), 4))
        return numerator / denominator
    
    def alpha073(self, open, high, low, volume, vwap):
        """
        Alpha073: (max(rank(decay_linear(delta(vwap, 5), 3)), rank(decay_linear(((delta(((open * 0.147155) + (low * (1 - 0.147155))), 2) / ((open * 0.147155) + (low * (1 - 0.147155)))) * -1), 3))) * -1)
        """
        part1 = self.rank(self.decay_linear(self.delta(vwap, 5), 3))
        
        base = open * 0.147155 + low * (1 - 0.147155)
        part2 = self.rank(self.decay_linear(
            (self.delta(base, 2) / base) * -1, 3))
        
        return np.maximum(part1, part2) * -1
    
    def alpha074(self, close, open, high, low, volume, adv30):
        """
        Alpha074: ((rank(ts_corr(close, ts_sum(adv30, 37), 15)) < rank(ts_corr(rank(((high * 0.0261661) + (vwap * (1 - 0.0261661)))), rank(volume), 11))) * -1)
        """
        # 简化版本
        vwap_approx = (high + low + open) / 3
        
        left = self.rank(self.ts_corr(close, self.ts_sum(adv30, 37), 15))
        right = self.rank(self.ts_corr(
            self.rank(high * 0.0261661 + vwap_approx * (1 - 0.0261661)),
            self.rank(volume), 11))
        return (left < right) * -1
    
    def alpha075(self, open, high, low, volume, adv50):
        """
        Alpha075: (rank(ts_corr(vwap, volume, 4)) < rank(ts_corr(rank(low), rank(adv50), 12)))
        """
        vwap_approx = (high + low + open) / 3
        
        left = self.rank(self.ts_corr(vwap_approx, volume, 4))
        right = self.rank(self.ts_corr(self.rank(low), self.rank(adv50), 12))
        return left < right
    
    def alpha076(self, close, open, high, low, volume):
        """
        Alpha076: (max(rank(decay_linear(delta(vwap, 1), 12)), rank(decay_linear(ts_rank(ts_corr(IndNeutralize(low, IndClass.industry), adv81, 8), 20), 17))) * -1)
        """
        # 简化版本，忽略行业中性化
        vwap_approx = (high + low + open) / 3
        adv81_approx = self.ts_mean(volume, 81)
        
        part1 = self.rank(self.decay_linear(self.delta(vwap_approx, 1), 12))
        part2 = self.rank(self.decay_linear(
            self.ts_rank(self.ts_corr(low, adv81_approx, 8), 20), 17))
        
        return np.maximum(part1, part2) * -1
    
    def alpha077(self, high, low, open, volume):
        """
        Alpha077: min(rank(decay_linear(((((high + low) / 2) + high) - (vwap + high)), 20)), rank(decay_linear(ts_corr(((high + low) / 2), adv40, 3), 6)))
        """
        vwap_approx = (high + low + open) / 3
        adv40_approx = self.ts_mean(volume, 40)
        
        part1 = self.rank(self.decay_linear(
            ((high + low) / 2 + high) - (vwap_approx + high), 20))
        part2 = self.rank(self.decay_linear(
            self.ts_corr((high + low) / 2, adv40_approx, 3), 6))
        
        return np.minimum(part1, part2)
    
    def alpha078(self, high, low, open, volume):
        """
        Alpha078: (rank(ts_corr(ts_sum(((low * 0.352233) + (vwap * (1 - 0.352233))), 20), ts_sum(((high * 0.021821) + (vwap * (1 - 0.021821))), 2), 6))^rank(ts_corr(rank(vwap), rank(volume), 6)))
        """
        vwap_approx = (high + low + open) / 3
        
        sum1 = self.ts_sum(low * 0.352233 + vwap_approx * (1 - 0.352233), 20)
        sum2 = self.ts_sum(high * 0.021821 + vwap_approx * (1 - 0.021821), 2)
        
        base = self.rank(self.ts_corr(sum1, sum2, 6))
        exponent = self.rank(self.ts_corr(self.rank(vwap_approx), self.rank(volume), 6))
        
        return base ** exponent
    
    def alpha079(self, open, high, low, volume):
        """
        Alpha079: (rank(delta(IndNeutralize(((close * 0.60733) + (open * (1 - 0.60733))), IndClass.industry), 1)) < rank(ts_corr(ts_rank(vwap, 4), ts_rank(adv150, 18), 18)))
        """
        # 简化版本
        close_approx = (high + low) / 2
        vwap_approx = (high + low + open) / 3
        adv150_approx = self.ts_mean(volume, 150)
        
        left = self.rank(self.delta(close_approx * 0.60733 + open * (1 - 0.60733), 1))
        right = self.rank(self.ts_corr(
            self.ts_rank(vwap_approx, 4), self.ts_rank(adv150_approx, 18), 18))
        
        return left < right
    
    def alpha080(self, open, high, low, volume):
        """
        Alpha080: ((rank(Sign(delta(IndNeutralize(((open * 0.868128) + (high * (1 - 0.868128))), IndClass.industry), 4)))< rank(ts_corr(high, adv10, 5))) * -1)
        """
        # 简化版本
        adv10_approx = self.ts_mean(volume, 10)
        
        left = self.rank(np.sign(self.delta(open * 0.868128 + high * (1 - 0.868128), 4)))
        right = self.rank(self.ts_corr(high, adv10_approx, 5))
        
        return (left < right) * -1
    
    def alpha081(self, high, low, open, volume):
        """
        Alpha081: ((rank(Log(product(rank((rank(ts_corr(vwap, ts_sum(adv10, 50), 8))^4)), 15))) < rank(ts_corr(rank(vwap), rank(volume), 5))) * -1)
        """
        vwap_approx = (high + low + open) / 3
        adv10_approx = self.ts_mean(volume, 10)
        
        inner = self.rank(self.ts_corr(vwap_approx, self.ts_sum(adv10_approx, 50), 8)) ** 4
        left = self.rank(np.log(self.product(self.rank(inner), 15)))
        right = self.rank(self.ts_corr(self.rank(vwap_approx), self.rank(volume), 5))
        
        return (left < right) * -1
    
    def alpha082(self, open, high, low, volume):
        """
        Alpha082: (min(rank(decay_linear(delta(open, 1), 15)), rank(decay_linear(ts_corr(IndNeutralize(volume, IndClass.industry), ((open * 0.634196) + (open * (1 - 0.634196))), 17),7))) * -1)
        """
        # 简化版本
        part1 = self.rank(self.decay_linear(self.delta(open, 1), 15))
        part2 = self.rank(self.decay_linear(
            self.ts_corr(volume, open, 17), 7))  # 简化处理
        
        return np.minimum(part1, part2) * -1
    
    def alpha083(self, high, low, volume):
        """
        Alpha083: ((rank(delay(((high - low) / (ts_sum(close, 5) / 5)), 2)) * rank(rank(volume))) / (((high - low) / (ts_sum(close, 5) / 5)) / (vwap - close)))
        """
        # 需要close，这里用high和low的均值近似
        close_approx = (high + low) / 2
        vwap_approx = close_approx  # 简化处理
        
        numerator = (self.rank(self.delay((high - low) / self.ts_mean(close_approx, 5), 2)) * 
                    self.rank(self.rank(volume)))
        denominator = ((high - low) / self.ts_mean(close_approx, 5)) / (vwap_approx - close_approx)
        
        return numerator / denominator
    
    def alpha084(self, close, volume):
        """
        Alpha084: SignedPower(ts_rank((vwap - ts_max(vwap, 15)), 21), delta(close, 5))
        """
        # 需要vwap，这里用close近似
        vwap_approx = close
        
        base = self.ts_rank(vwap_approx - self.ts_max(vwap_approx, 15), 21)
        exponent = self.delta(close, 5)
        
        return self.signed_power(base, exponent)
    
    def alpha085(self, close, volume):
        """
        Alpha085: (rank(ts_corr(((high * 0.876703) + (close * (1 - 0.876703))), volume, 6))^rank(ts_corr(rank(volume), rank(high), 5)))
        """
        # 需要high，这里用close近似处理
        high_approx = close * 1.02  # 简化处理
        
        base = self.rank(self.ts_corr(
            high_approx * 0.876703 + close * (1 - 0.876703), volume, 6))
        exponent = self.rank(self.ts_corr(self.rank(volume), self.rank(high_approx), 5))
        
        return base ** exponent
    
    def alpha086(self, close, volume):
        """
        Alpha086: ((ts_rank(ts_corr(rank(close), rank(adv20), 5), 9) < rank(((ts_argmax(vwap, 13) - 5) / 10))) * -1)
        """
        # 简化版本
        adv20_approx = self.ts_mean(volume, 20)
        vwap_approx = close
        
        left = self.ts_rank(self.ts_corr(
            self.rank(close), self.rank(adv20_approx), 5), 9)
        right = self.rank((self.ts_argmax(vwap_approx, 13) - 5) / 10)
        
        return (left < right) * -1
    
    def alpha087(self, high, low, open, volume):
        """
        Alpha087: (max(rank(decay_linear(delta(((close * 0.369701) + (vwap * (1 - 0.369701))), 1), 8)), ts_rank(decay_linear(ts_corr(ts_rank(adv81, 13), ts_rank(low, 7), 8), 20), 16)) * -1)
        """
        # 简化版本
        close_approx = (high + low) / 2
        vwap_approx = (high + low + open) / 3
        adv81_approx = self.ts_mean(volume, 81)
        
        part1 = self.rank(self.decay_linear(
            self.delta(close_approx * 0.369701 + vwap_approx * (1 - 0.369701), 1), 8))
        part2 = self.ts_rank(self.decay_linear(
            self.ts_corr(self.ts_rank(adv81_approx, 13), self.ts_rank(low, 7), 8), 20), 16)
        
        return np.maximum(part1, part2) * -1
    
    def alpha088(self, close, open, high, low, volume):
        """
        Alpha088: min(rank(decay_linear(((rank(open) + rank(low)) - (rank(high) + rank(close))), 8)), ts_rank(decay_linear(ts_corr(ts_rank(close, 8), ts_rank(adv60, 20), 8), 7), 3))
        """
        adv60_approx = self.ts_mean(volume, 60)
        
        part1 = self.rank(self.decay_linear(
            (self.rank(open) + self.rank(low)) - (self.rank(high) + self.rank(close)), 8))
        part2 = self.ts_rank(self.decay_linear(
            self.ts_corr(self.ts_rank(close, 8), self.ts_rank(adv60_approx, 20), 8), 7), 3)
        
        return np.minimum(part1, part2)
    
    def alpha089(self, close, open, high, low, volume):
        """
        Alpha089: (ts_rank(decay_linear(ts_corr(((low * 0.967285) + (low * (1 - 0.967285))), adv10, 7), 5),8) - ts_rank(decay_linear(delta(IndNeutralize(vwap, IndClass.industry), 4), 16), 8))
        """
        # 简化版本
        adv10_approx = self.ts_mean(volume, 10)
        vwap_approx = (high + low + open) / 3
        
        part1 = self.ts_rank(self.decay_linear(
            self.ts_corr(low, adv10_approx, 7), 5), 8)  # 简化处理
        part2 = self.ts_rank(self.decay_linear(
            self.delta(vwap_approx, 4), 16), 8)
        
        return part1 - part2
    
    def alpha090(self, close, open, high, low, volume):
        """
        Alpha090: ((rank((close - ts_max(close, 5)))* rank(ts_corr(IndNeutralize(adv40, IndClass.industry), low, 5))) * -1)
        """
        # 简化版本
        adv40_approx = self.ts_mean(volume, 40)
        
        part1 = self.rank(close - self.ts_max(close, 5))
        part2 = self.rank(self.ts_corr(adv40_approx, low, 5))
        
        return part1 * part2 * -1
    
    def alpha091(self, close, open, high, low, volume):
        """
        Alpha091: ((ts_rank(decay_linear(decay_linear(ts_corr(IndNeutralize(close, IndClass.industry), volume, 10), 16), 4), 5) - rank(decay_linear(ts_corr(vwap, adv30, 4), 3))) * -1)
        """
        # 简化版本
        vwap_approx = (high + low + open) / 3
        adv30_approx = self.ts_mean(volume, 30)
        
        part1 = self.ts_rank(self.decay_linear(
            self.decay_linear(self.ts_corr(close, volume, 10), 16), 4), 5)
        part2 = self.rank(self.decay_linear(
            self.ts_corr(vwap_approx, adv30_approx, 4), 3))
        
        return (part1 - part2) * -1
    
    def alpha092(self, close, open, high, low, volume):
        """
        Alpha092: min(ts_rank(decay_linear(((((high + low) / 2) + close) < (low + open)), 15), 18), ts_rank(decay_linear(ts_corr(rank(low), rank(adv30), 8), 7), 7))
        """
        adv30_approx = self.ts_mean(volume, 30)
        
        part1 = self.ts_rank(self.decay_linear(
            ((high + low) / 2 + close) < (low + open), 15), 18)
        part2 = self.ts_rank(self.decay_linear(
            self.ts_corr(self.rank(low), self.rank(adv30_approx), 8), 7), 7)
        
        return np.minimum(part1, part2)
    
    def alpha093(self, close, open, high, low, volume):
        """
        Alpha093: (ts_rank(decay_linear(ts_corr(IndNeutralize(vwap, IndClass.industry), adv81, 17), 4), 16) / rank(decay_linear(delta(((close * 0.524434) + (vwap * (1 - 0.524434))), 1), 50)))
        """
        # 简化版本
        vwap_approx = (high + low + open) / 3
        adv81_approx = self.ts_mean(volume, 81)
        
        numerator = self.ts_rank(self.decay_linear(
            self.ts_corr(vwap_approx, adv81_approx, 17), 4), 16)
        denominator = self.rank(self.decay_linear(
            self.delta(close * 0.524434 + vwap_approx * (1 - 0.524434), 1), 50))
        
        return numerator / denominator
    
    def alpha094(self, close, volume):
        """
        Alpha094: ((-1 * rank(decayLinear(delta(vwap, 5), 3))) * ts_rank(ts_argmin(ts_corr(indneutralize(volume, IndClass.sector), indneutralize(adv20, IndClass.sector), 6), 4), 8))
        """
        # 简化版本，忽略行业中性化
        adv20 = self.ts_mean(volume, 20)
        vwap = (close + close.shift(1)) / 2  # 简化的vwap
        
        part1 = -1 * self.rank(self.decay_linear(self.delta(vwap, 5), 3))
        corr = self.ts_corr(volume, adv20, 6)
        part2 = self.ts_rank(self.ts_argmin(corr, 4), 8)
        
        return part1 * part2
    
    def alpha095(self, volume):
        """
        Alpha095: (rank((open - ts_min(open, 12))) < rank((rank(ts_corr(ts_sum(((high + low) / 2), 19), ts_sum(adv40, 19), 13))^5)))
        """
        # 需要open, high, low数据，这里用简化版本
        adv40 = self.ts_mean(volume, 40)
        
        # 由于缺少open, high, low，返回简化版本
        return self.rank(self.ts_corr(volume, adv40, 13)) ** 5
    
    def alpha096(self, close, volume):
        """
        Alpha096: (max(ts_rank(decayLinear(ts_corr(rank(vwap), rank(volume), 4), 4), 8), ts_rank(decayLinear(ts_argmax(ts_corr(ts_rank(high, 13), ts_rank(volume, 13), 5), 3), 3), 9)) * -1)
        """
        # 简化版本
        vwap = close  # 简化的vwap
        
        part1 = self.ts_rank(self.decay_linear(self.ts_corr(self.rank(vwap), self.rank(volume), 4), 4), 8)
        part2 = self.ts_rank(self.decay_linear(self.ts_argmax(self.ts_corr(self.rank(close), self.rank(volume), 5), 3), 3), 9)
        
        return np.maximum(part1, part2) * -1
    
    def alpha097(self, low, volume):
        """
        Alpha097: ((rank(decayLinear(delta(IndNeutralize(((low * 0.721649) + (vwap * (1 - 0.721649))), IndClass.industry), 3.5), 20)) * -1) * ts_rank(ts_argmin(ts_corr(ts_rank(low, 8), ts_rank(adv81, 8), 7), 4), 6))
        """
        # 简化版本，忽略行业中性化
        adv81 = self.ts_mean(volume, 81)
        vwap = low  # 简化的vwap
        
        weighted_price = low * 0.721649 + vwap * (1 - 0.721649)
        part1 = self.rank(self.decay_linear(self.delta(weighted_price, 4), 20)) * -1  # 修复：3.5改为4
        part2 = self.ts_rank(self.ts_argmin(self.ts_corr(self.ts_rank(low, 8), self.ts_rank(adv81, 8), 7), 4), 6)
        
        return part1 * part2
    
    def alpha098(self, open, volume):
        """
        Alpha098: (rank(decayLinear(ts_corr(vwap, ts_sum(adv5, 26), 4), 7)) - rank(decayLinear(ts_rank(ts_argmin(ts_corr(rank(open), rank(adv15), 21), 9), 7), 8)))
        """
        adv5 = self.ts_mean(volume, 5)
        adv15 = self.ts_mean(volume, 15)
        vwap = open  # 简化的vwap
        
        part1 = self.rank(self.decay_linear(self.ts_corr(vwap, self.ts_sum(adv5, 26), 4), 7))
        part2 = self.rank(self.decay_linear(self.ts_rank(self.ts_argmin(self.ts_corr(self.rank(open), self.rank(adv15), 21), 9), 7), 8))
        
        return part1 - part2
    
    def alpha099(self, close, volume):
        """
        Alpha099: ((rank(ts_corr(ts_sum(((high + low) / 2), 20), ts_sum(volume, 20), 9)) < rank(ts_corr(low, volume, 6))) * -1)
        """
        # 简化版本，用close代替high和low
        mid_price = close
        
        part1 = self.rank(self.ts_corr(self.ts_sum(mid_price, 20), self.ts_sum(volume, 20), 9))
        part2 = self.rank(self.ts_corr(close, volume, 6))
        
        return (part1 < part2) * -1
    
    def alpha100(self, close, volume):
        """
        Alpha100: (0 - (1 * (((1.5 * scale(indneutralize(indneutralize(rank(((((close - low) - (high - close)) / (high - low)) * volume)), IndClass.subindustry), IndClass.subindustry))) - scale(indneutralize((ts_corr(close, rank(adv20), 5) - rank(ts_argmin(close, 30))), IndClass.subindustry))) * (volume / adv20))))
        """
        # 简化版本，忽略行业中性化
        adv20 = self.ts_mean(volume, 20)
        high, low = close * 1.01, close * 0.99  # 简化的high和low
        
        price_ratio = ((close - low) - (high - close)) / (high - low)
        part1 = 1.5 * self.scale(self.rank(price_ratio * volume))
        part2 = self.scale(self.ts_corr(close, self.rank(adv20), 5) - self.rank(self.ts_argmin(close, 30)))
        
        return 0 - (part1 - part2) * (volume / adv20)
    
    def alpha101(self, close, open, volume):
        """
        Alpha101: ((close - open) / ((high - low) + .001))
        """
        # 简化版本，用close估算high和low
        high, low = close * 1.01, close * 0.99
        return (close - open) / ((high - low) + 0.001)
    
    # ======================核心相关性分析函数======================

    def calculate_factor_correlation_matrix(self, factor_dict):
        """
        计算因子相关性矩阵 - 最简单直接的方法
        """
        import pandas as pd
        import numpy as np
        from scipy.stats import pearsonr

        print(f"📊 开始计算 {len(factor_dict)} 个因子的相关性矩阵...")

        # 第一步：提取并清理所有因子数据
        clean_factors = {}

        for factor_name, factor_data in factor_dict.items():
            try:
                # 统一转换为numpy数组
                if isinstance(factor_data, np.ndarray):
                    values = factor_data.flatten()
                elif isinstance(factor_data, pd.DataFrame):
                    values = factor_data.values.flatten()
                elif isinstance(factor_data, pd.Series):
                    values = factor_data.values
                else:
                    values = np.array(factor_data).flatten()

                # 转换为float类型
                values = values.astype(float)

                # 移除NaN和无限值
                values = values[np.isfinite(values)]

                # 检查数据有效性
                if len(values) == 0:
                    print(f"⚠️ 因子 {factor_name}: 没有有效数据")
                    continue

                if np.var(values) == 0:
                    print(f"⚠️ 因子 {factor_name}: 方差为0，跳过")
                    continue

                clean_factors[factor_name] = values
                print(f"✅ 因子 {factor_name}: {len(values)} 个有效数据点")

            except Exception as e:
                print(f"❌ 处理因子 {factor_name} 失败: {e}")
                continue

        if len(clean_factors) < 2:
            print("❌ 有效因子数量不足")
            return None

        # 第二步：数据对齐
        factor_names = list(clean_factors.keys())
        min_length = min(len(data) for data in clean_factors.values())

        print(f"📏 对齐长度: {min_length}")

        if min_length < 10:  # 至少需要10个数据点
            print("❌ 数据点不足以计算可靠的相关性")
            return None

        # 第三步：创建对齐的数据矩阵
        aligned_data = {}
        for name in factor_names:
            aligned_data[name] = clean_factors[name][:min_length]

        # 第四步：手动计算相关性矩阵
        n_factors = len(factor_names)
        correlation_matrix = np.zeros((n_factors, n_factors))

        print("🔢 计算相关性系数...")

        for i in range(n_factors):
            for j in range(n_factors):
                if i == j:
                    correlation_matrix[i, j] = 1.0
                else:
                    try:
                        data1 = aligned_data[factor_names[i]]
                        data2 = aligned_data[factor_names[j]]

                        # 使用numpy计算相关性
                        correlation = np.corrcoef(data1, data2)[0, 1]

                        # 检查结果有效性
                        if np.isnan(correlation):
                            correlation = 0.0

                        correlation_matrix[i, j] = correlation

                    except Exception as e:
                        print(f"⚠️ 计算 {factor_names[i]} 和 {factor_names[j]} 的相关性失败: {e}")
                        correlation_matrix[i, j] = 0.0

        # 第五步：转换为DataFrame
        corr_df = pd.DataFrame(
            correlation_matrix,
            index=factor_names,
            columns=factor_names
        )

        print(f"✅ 相关性矩阵计算完成，大小: {corr_df.shape}")

        return corr_df
    
    def calculate_factor_return_correlation(self, factor_dict, return_window=5):
        """
        计算因子与未来收益的相关性
        Args:
            factor_dict: 因子字典
            return_window: 未来收益窗口期
        Returns:
            DataFrame: 因子与收益的相关性
        """
        data = self.data_cache
        future_returns = data['returns'].shift(-return_window)
        
        correlations = {}
        for name, factor in factor_dict.items():
            # 计算每个交易日的截面相关性
            daily_corrs = []
            for date in factor.index:
                if date in future_returns.index:
                    factor_values = factor.loc[date].dropna()
                    return_values = future_returns.loc[date].dropna()
                    
                    # 找到共同的股票
                    common_stocks = factor_values.index.intersection(return_values.index)
                    if len(common_stocks) > 10:  # 至少10只股票
                        corr, p_value = pearsonr(
                            factor_values[common_stocks], 
                            return_values[common_stocks]
                        )
                        daily_corrs.append({
                            'date': date,
                            'correlation': corr,
                            'p_value': p_value,
                            'sample_size': len(common_stocks)
                        })
            
            if daily_corrs:
                correlations[name] = pd.DataFrame(daily_corrs)
        
        return correlations
    
    def calculate_rolling_correlation(self, factor1, factor2, window=20):
        """
        计算两个因子的滚动相关性
        Args:
            factor1: 因子1 DataFrame
            factor2: 因子2 DataFrame
            window: 滚动窗口
        Returns:
            DataFrame: 滚动相关性
        """
        # 确保两个因子具有相同的索引
        common_dates = factor1.index.intersection(factor2.index)
        common_stocks = factor1.columns.intersection(factor2.columns)
        
        factor1_aligned = factor1.loc[common_dates, common_stocks]
        factor2_aligned = factor2.loc[common_dates, common_stocks]
        
        # 计算每只股票的滚动相关性
        rolling_corr = pd.DataFrame(index=common_dates, columns=common_stocks)
        
        for stock in common_stocks:
            stock_corr = factor1_aligned[stock].rolling(window).corr(factor2_aligned[stock])
            rolling_corr[stock] = stock_corr
        
        return rolling_corr
    
    def factor_stability_analysis(self, factor_dict, window=60):
        """
        因子稳定性分析
        Args:
            factor_dict: 因子字典
            window: 分析窗口
        Returns:
            dict: 稳定性指标
        """
        stability_metrics = {}
        
        for name, factor in factor_dict.items():
            # 计算因子的时间序列稳定性
            factor_mean = factor.mean(axis=1)  # 每日平均值
            factor_std = factor.std(axis=1)    # 每日标准差
            
            # 滚动稳定性指标
            rolling_mean = factor_mean.rolling(window).mean()
            rolling_std = factor_std.rolling(window).std()
            
            # 计算稳定性得分
            stability_score = rolling_std / rolling_mean.abs()
            
            stability_metrics[name] = {
                'mean_stability': rolling_mean,
                'std_stability': rolling_std,
                'stability_score': stability_score,
                'avg_stability_score': stability_score.mean()
            }
        
        return stability_metrics
    
    def calculate_cross_sectional_correlation(self, factor_dict, date_list=None):
        """
        计算截面相关性（特定日期的股票截面相关性）
        Args:
            factor_dict: 因子字典
            date_list: 分析日期列表，如果为None则使用全部日期
        Returns:
            dict: 每个日期的相关性矩阵
        """
        if date_list is None:
            # 选择所有因子都有数据的日期
            common_dates = None
            for factor in factor_dict.values():
                if common_dates is None:
                    common_dates = factor.index
                else:
                    common_dates = common_dates.intersection(factor.index)
            date_list = common_dates
        
        cross_sectional_corr = {}
        
        for date in date_list:
            daily_factors = pd.DataFrame()
            for name, factor in factor_dict.items():
                if date in factor.index:
                    daily_factors[name] = factor.loc[date]
            
            if not daily_factors.empty:
                cross_sectional_corr[date] = daily_factors.corr()
        
        return cross_sectional_corr

    def positive_correlation_analysis(self, factor_dict, threshold=0.8, min_factors=5):
        """
        正相关性分析 - 添加更多错误检查
        """
        print(f"🔍 开始正相关性分析 (阈值: {threshold})")

        # 计算相关性矩阵
        corr_matrix = self.calculate_factor_correlation_matrix(factor_dict)

        # 检查相关性矩阵是否成功计算
        if corr_matrix is None:
            print("❌ 相关性矩阵计算失败")
            return {
                'correlation_matrix': None,
                'high_correlation_pairs': [],
                'high_corr_pairs': [],
                'factor_groups': [],
                'summary': "相关性矩阵计算失败"
            }

        # 寻找高相关性因子对
        high_correlation_pairs = []
        factor_names = corr_matrix.index.tolist()

        try:
            for i in range(len(factor_names)):
                for j in range(i + 1, len(factor_names)):
                    factor1 = factor_names[i]
                    factor2 = factor_names[j]

                    correlation = corr_matrix.iloc[i, j]

                    if not np.isnan(correlation) and abs(correlation) >= threshold:
                        high_correlation_pairs.append({
                            'factor1': factor1,
                            'factor2': factor2,
                            'correlation': correlation
                        })

            # 按相关性绝对值排序
            high_correlation_pairs.sort(key=lambda x: abs(x['correlation']), reverse=True)

            print(f"✅ 发现 {len(high_correlation_pairs)} 个高相关性因子对")

            # 显示前几个结果
            for i, pair in enumerate(high_correlation_pairs[:5]):
                print(f"  {i + 1}. {pair['factor1']} <-> {pair['factor2']}: {pair['correlation']:.4f}")

            # 因子分组
            factor_groups = self.group_correlated_factors(high_correlation_pairs, min_factors)

            return {
                'correlation_matrix': corr_matrix,
                'high_correlation_pairs': high_correlation_pairs,
                'high_corr_pairs': high_correlation_pairs,
                'factor_groups': factor_groups,
                'summary': f"发现 {len(high_correlation_pairs)} 个高相关性因子对，{len(factor_groups)} 个因子组"
            }

        except Exception as e:
            print(f"❌ 正相关性分析出错: {e}")
            import traceback
            traceback.print_exc()
            return {
                'correlation_matrix': corr_matrix,
                'high_correlation_pairs': [],
                'high_corr_pairs': [],
                'factor_groups': [],
                'summary': f"分析出错: {e}"
            }
    
    def plot_correlation_heatmap(self, factor_dict, figsize=(10, 8), save_path=None):
        """
        绘制因子相关性热力图
        """
        corr_matrix = self.calculate_factor_correlation_matrix(factor_dict)
        
        plt.figure(figsize=figsize)
        sns.heatmap(corr_matrix, annot=True, cmap='RdYlBu_r', center=0, 
                   square=True, fmt='.3f', cbar_kws={'label': 'Correlation'})
        plt.title('Factor Correlation Matrix', fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def comprehensive_correlation_report(self, factor_dict, return_window=5):
        """
        综合相关性分析报告
        """
        print("=" * 50)
        print("综合因子相关性分析报告")
        print("=" * 50)
        
        # 1. 因子间相关性分析
        print("\n1. 因子间相关性分析")
        print("-" * 30)
        positive_analysis = self.positive_correlation_analysis(factor_dict)
        
        print(f"高正相关因子对 (相关性 > 0.5):")
        for pair in positive_analysis['high_corr_pairs'][:5]:
            print(f"  {pair['factor1']} vs {pair['factor2']}: {pair['correlation']:.3f}")
        
        print(f"\n各因子平均正相关性:")
        for factor, avg_corr in positive_analysis['avg_positive_correlation'].items():
            print(f"  {factor}: {avg_corr:.3f}")
        
        # 2. 因子-收益相关性分析
        print("\n2. 因子-收益相关性分析")
        print("-" * 30)
        factor_return_corr = self.calculate_factor_return_correlation(factor_dict, return_window)
        
        for name, corr_df in factor_return_corr.items():
            avg_corr = corr_df['correlation'].mean()
            print(f"  {name}: {avg_corr:.3f} (平均相关性)")
        
        # 3. 因子稳定性分析
        print("\n3. 因子稳定性分析")
        print("-" * 30)
        stability_metrics = self.factor_stability_analysis(factor_dict)
        
        for name, metrics in stability_metrics.items():
            print(f"  {name}: {metrics['avg_stability_score']:.3f} (稳定性得分)")
        
        print("\n" + "=" * 50)
        print("分析完成")
        print("=" * 50)
        
        return {
            'positive_analysis': positive_analysis,
            'factor_return_correlation': factor_return_corr,
            'stability_metrics': stability_metrics
        }

    # ✅ 改进的自动获取所有alpha方法
    def get_all_alpha_factors(self, data):
        """自动计算所有alpha因子 - 改进版本，处理参数不统一问题"""
        factors = {}
        
        # 预计算常用的衍生变量
        print("预计算常用变量...")
        close = data['close']
        open_price = data['open']
        high = data['high']
        low = data['low']
        volume = data['volume']
        returns = data['returns']
        vwap = data['vwap']
        
        # 计算各种平均成交量
        adv5 = self.ts_mean(volume, 5)
        adv10 = self.ts_mean(volume, 10) 
        adv15 = self.ts_mean(volume, 15)
        adv20 = self.ts_mean(volume, 20)
        adv30 = self.ts_mean(volume, 30)
        adv40 = self.ts_mean(volume, 40)
        adv50 = self.ts_mean(volume, 50)
        adv60 = self.ts_mean(volume, 60)
        adv81 = self.ts_mean(volume, 81)
        adv150 = self.ts_mean(volume, 150)
        adv180 = self.ts_mean(volume, 180)
        
        # 获取所有以'alpha'开头的方法
        alpha_methods = [method for method in dir(self) 
                        if method.startswith('alpha') and callable(getattr(self, method))]
        
        print(f"发现 {len(alpha_methods)} 个alpha方法")
        
        for method_name in alpha_methods:
            try:
                method = getattr(self, method_name)
                
                # 🔥 核心改进：使用inspect模块获取方法签名
                import inspect
                sig = inspect.signature(method)
                params = list(sig.parameters.keys())
                
                # 排除self参数
                if 'self' in params:
                    params.remove('self')
                
                # 根据参数名称准备参数字典
                kwargs = {}
                for param in params:
                    if param == 'data':
                        kwargs[param] = data
                    elif param == 'close':
                        kwargs[param] = close
                    elif param == 'open':
                        kwargs[param] = open_price
                    elif param == 'high':
                        kwargs[param] = high
                    elif param == 'low':
                        kwargs[param] = low
                    elif param == 'volume':
                        kwargs[param] = volume
                    elif param == 'returns':
                        kwargs[param] = returns
                    elif param == 'vwap':
                        kwargs[param] = vwap
                    elif param == 'adv5':
                        kwargs[param] = adv5
                    elif param == 'adv10':
                        kwargs[param] = adv10
                    elif param == 'adv15':
                        kwargs[param] = adv15
                    elif param == 'adv20':
                        kwargs[param] = adv20
                    elif param == 'adv30':
                        kwargs[param] = adv30
                    elif param == 'adv40':
                        kwargs[param] = adv40
                    elif param == 'adv50':
                        kwargs[param] = adv50
                    elif param == 'adv60':
                        kwargs[param] = adv60
                    elif param == 'adv81':
                        kwargs[param] = adv81
                    elif param == 'adv150':
                        kwargs[param] = adv150
                    elif param == 'adv180':
                        kwargs[param] = adv180
                    else:
                        # 如果是未知参数，尝试从data中获取
                        if param in data:
                            kwargs[param] = data[param]
                        else:
                            print(f"⚠️ {method_name} 需要未知参数: {param}")
                            continue
                
                # 调用方法
                if len(kwargs) == len(params):  # 所有参数都准备好了
                    factors[method_name] = method(**kwargs)
                    print(f"✓ {method_name} 计算完成")
                else:
                    print(f"⚠️ {method_name} 参数不完整，跳过")
                
            except Exception as e:
                print(f"✗ {method_name} 计算失败: {e}")
        
        print(f"成功计算 {len(factors)} 个因子")
        return factors

    # ✅ 新增：更智能的参数映射方法
    def get_alpha_factors_smart(self, data, factor_list=None):
        """
        智能计算alpha因子 - 支持指定因子列表
        Args:
            data: 数据字典
            factor_list: 要计算的因子列表，如果为None则计算所有
        """
        import inspect
        
        factors = {}
        
        # 预计算所有可能需要的变量
        variable_cache = self._prepare_all_variables(data)
        
        # 获取要计算的方法列表
        if factor_list is None:
            alpha_methods = [method for method in dir(self) 
                            if method.startswith('alpha') and callable(getattr(self, method))]
        else:
            alpha_methods = [f'alpha{str(i).zfill(3)}' for i in factor_list if hasattr(self, f'alpha{str(i).zfill(3)}')]
        
        print(f"准备计算 {len(alpha_methods)} 个因子")
        
        for method_name in alpha_methods:
            try:
                method = getattr(self, method_name)
                
                # 获取方法签名
                sig = inspect.signature(method)
                params = [p for p in sig.parameters.keys() if p != 'self']
                
                # 智能参数映射
                kwargs = self._smart_parameter_mapping(params, variable_cache)
                
                if kwargs is not None:
                    factors[method_name] = method(**kwargs)
                    print(f"✓ {method_name} 计算完成")
                else:
                    print(f"⚠️ {method_name} 参数映射失败")
                
            except Exception as e:
                print(f"✗ {method_name} 计算失败: {e}")
        
        return factors

    def _prepare_all_variables(self, data):
        """预计算所有可能需要的变量"""
        cache = {}
        
        # 基础变量
        cache.update(data)
        
        # 衍生变量
        if 'volume' in data:
            volume = data['volume']
            for days in [5, 10, 15, 20, 30, 40, 50, 60, 81, 150, 180]:
                cache[f'adv{days}'] = self.ts_mean(volume, days)
        
        # 其他常用变量
        if 'close' in data and 'open' in data:
            cache['open_price'] = data['open']  # 避免关键字冲突
        
        return cache

    def _smart_parameter_mapping(self, params, variable_cache):
        """智能参数映射"""
        kwargs = {}
        
        for param in params:
            if param in variable_cache:
                kwargs[param] = variable_cache[param]
            elif param == 'open':
                kwargs[param] = variable_cache.get('open_price', variable_cache.get('open'))
            else:
                # 尝试一些常见的参数映射
                mapped_param = self._try_parameter_mapping(param, variable_cache)
                if mapped_param is not None:
                    kwargs[param] = mapped_param
                else:
                    print(f"⚠️ 无法映射参数: {param}")
                    return None
        
        return kwargs

    def _try_parameter_mapping(self, param, variable_cache):
        """尝试参数映射"""
        # 常见的参数映射规则
        mapping_rules = {
            'open_price': 'open',
            'close_price': 'close',
            'high_price': 'high',
            'low_price': 'low',
            'vol': 'volume',
            'ret': 'returns',
            'pct_change': 'returns'
        }
        
        if param in mapping_rules:
            return variable_cache.get(mapping_rules[param])
        
        return None

    # ✅ 新增：分组计算方法（按参数相似性）
    def calculate_factors_by_groups(self, data):
        """按参数相似性分组计算因子"""
        import inspect
        
        # 按参数类型分组
        groups = {
            'data_only': [],           # 只需要data参数
            'basic_ohlcv': [],         # 需要基础OHLCV数据
            'with_volume_avg': [],     # 需要平均成交量
            'complex': []              # 复杂参数需求
        }
        
        # 获取所有alpha方法
        alpha_methods = [method for method in dir(self) 
                        if method.startswith('alpha') and callable(getattr(self, method))]
        
        # 分组
        for method_name in alpha_methods:
            method = getattr(self, method_name)
            sig = inspect.signature(method)
            params = [p for p in sig.parameters.keys() if p != 'self']
            
            if len(params) == 1 and 'data' in params:
                groups['data_only'].append(method_name)
            elif all(p in ['close', 'open', 'high', 'low', 'volume', 'returns', 'vwap'] for p in params):
                groups['basic_ohlcv'].append(method_name)
            elif any(p.startswith('adv') for p in params):
                groups['with_volume_avg'].append(method_name)
            else:
                groups['complex'].append(method_name)
        
        print("因子分组结果:")
        for group_name, methods in groups.items():
            print(f"  {group_name}: {len(methods)} 个因子")
        
        # 分组计算
        all_factors = {}
        
        # 1. 计算只需要data的因子
        print("\n计算 data_only 组...")
        for method_name in groups['data_only']:
            try:
                method = getattr(self, method_name)
                all_factors[method_name] = method(data)
                print(f"✓ {method_name}")
            except Exception as e:
                print(f"✗ {method_name}: {e}")
        
        # 2. 计算基础OHLCV因子
        print("\n计算 basic_ohlcv 组...")
        variable_cache = self._prepare_all_variables(data)
        for method_name in groups['basic_ohlcv']:
            try:
                method = getattr(self, method_name)
                sig = inspect.signature(method)
                params = [p for p in sig.parameters.keys() if p != 'self']
                kwargs = {p: variable_cache[p] for p in params if p in variable_cache}
                
                if len(kwargs) == len(params):
                    all_factors[method_name] = method(**kwargs)
                    print(f"✓ {method_name}")
                else:
                    print(f"⚠️ {method_name}: 参数不完整")
            except Exception as e:
                print(f"✗ {method_name}: {e}")
        
        # 3. 计算需要平均成交量的因子
        print("\n计算 with_volume_avg 组...")
        for method_name in groups['with_volume_avg']:
            try:
                method = getattr(self, method_name)
                kwargs = self._smart_parameter_mapping(
                    [p for p in inspect.signature(method).parameters.keys() if p != 'self'],
                    variable_cache
                )
                if kwargs:
                    all_factors[method_name] = method(**kwargs)
                    print(f"✓ {method_name}")
            except Exception as e:
                print(f"✗ {method_name}: {e}")
        
        # 4. 计算复杂因子
        print("\n计算 complex 组...")
        for method_name in groups['complex']:
            try:
                method = getattr(self, method_name)
                kwargs = self._smart_parameter_mapping(
                    [p for p in inspect.signature(method).parameters.keys() if p != 'self'],
                    variable_cache
                )
                if kwargs:
                    all_factors[method_name] = method(**kwargs)
                    print(f"✓ {method_name}")
            except Exception as e:
                print(f"✗ {method_name}: {e}")
        
        return all_factors

    # ✅ 分批处理大量因子（避免内存溢出）
    def calculate_factors_in_batches(self, data, batch_size=20):
        """分批计算因子"""
        all_factors = {}
        alpha_methods = [method for method in dir(self) 
                        if method.startswith('alpha') and callable(getattr(self, method))]
        
        for i in range(0, len(alpha_methods), batch_size):
            batch = alpha_methods[i:i+batch_size]
            print(f"处理批次 {i//batch_size + 1}: {len(batch)} 个因子")
            
            batch_factors = {}
            for method_name in batch:
                try:
                    method = getattr(self, method_name)
                    batch_factors[method_name] = method(data)
                except Exception as e:
                    print(f"✗ {method_name} 失败: {e}")
            
            all_factors.update(batch_factors)
            
            # 可选：每批次后进行相关性分析
            if batch_factors:
                batch_corr = self.calculate_factor_correlation_matrix(batch_factors)
                print(f"批次 {i//batch_size + 1} 相关性矩阵计算完成")
        
        return all_factors

    def analyze_101_factors(self, data):
        """
        完整的101因子分析流程 - 添加更多错误检查
        """
        print("🧮 开始计算Alpha101因子...")

        try:
            # 获取所有因子
            factor_dict = self.get_all_alpha_factors(data)

            if not factor_dict:
                print("❌ 没有成功计算出任何因子")
                return None

            print(f"✅ 成功计算 {len(factor_dict)} 个因子")

            # 正相关性分析
            print("\n📊 开始正相关性分析...")
            positive_analysis = self.positive_correlation_analysis(
                factor_dict,
                threshold=0.8,
                min_factors=5
            )

            # 识别冗余因子
            print("\n🔍 开始识别冗余因子...")
            redundant_factors = self.identify_redundant_factors(factor_dict, threshold=0.8)

            # 汇总结果
            result = {
                'factor_dict': factor_dict,
                'positive_analysis': positive_analysis,
                'redundant_factors': redundant_factors,
                'high_correlation_pairs': positive_analysis['high_correlation_pairs'],
                'factor_groups': positive_analysis['factor_groups'],
                'summary': {
                    'total_factors': len(factor_dict),
                    'high_corr_pairs': len(positive_analysis['high_correlation_pairs']),
                    'redundant_factors': len(redundant_factors),
                    'factor_groups': len(positive_analysis['factor_groups'])
                }
            }

            print(f"\n✅ 分析完成!")
            print(f"   - 总因子数: {result['summary']['total_factors']}")
            print(f"   - 高相关性因子对: {result['summary']['high_corr_pairs']}")
            print(f"   - 冗余因子: {result['summary']['redundant_factors']}")
            print(f"   - 因子组: {result['summary']['factor_groups']}")

            return result

        except Exception as e:
            print(f"❌ 分析过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return None

    # 调试辅助函数
    def debug_factor_data(self, factor_dict):
        """
        调试因子数据结构
        """
        print("🔍 调试因子数据结构:")

        for name, data in list(factor_dict.items())[:5]:  # 只检查前5个
            print(f"\n因子: {name}")
            print(f"  类型: {type(data)}")
            print(f"  形状: {getattr(data, 'shape', 'N/A')}")

            if hasattr(data, 'index'):
                print(f"  索引类型: {type(data.index)}")
                print(f"  索引: {data.index}")

            if hasattr(data, 'values'):
                sample_values = data.values.flatten()[:5]
                print(f"  样本值: {sample_values}")
            else:
                print(f"  样本值: {data[:5] if hasattr(data, '__getitem__') else 'N/A'}")

    def identify_redundant_factors(self, factor_dict, threshold=0.8):
        """
        识别冗余因子 - 添加空值检查
        """
        print(f"🔍 开始识别冗余因子 (阈值: {threshold})")

        # 计算相关性矩阵
        corr_matrix = self.calculate_factor_correlation_matrix(factor_dict)

        # 检查相关性矩阵是否成功计算
        if corr_matrix is None:
            print("❌ 相关性矩阵计算失败，无法识别冗余因子")
            return []

        redundant_factors = []
        processed_factors = set()

        try:
            factor_names = corr_matrix.index.tolist()

            for i, factor1 in enumerate(factor_names):
                if factor1 in processed_factors:
                    continue

                for j, factor2 in enumerate(factor_names):
                    if i >= j or factor2 in processed_factors:
                        continue

                    correlation = corr_matrix.iloc[i, j]

                    if abs(correlation) >= threshold:
                        # 选择保留哪个因子的逻辑
                        # 这里简单地保留名称较短的因子
                        if len(factor1) <= len(factor2):
                            redundant_factors.append(factor2)
                            processed_factors.add(factor2)
                        else:
                            redundant_factors.append(factor1)
                            processed_factors.add(factor1)
                            break

            print(f"✅ 识别出 {len(redundant_factors)} 个冗余因子")

            return redundant_factors

        except Exception as e:
            print(f"❌ 识别冗余因子时出错: {e}")
            return []
# ✅ 完整的使用流程
def main_analysis():
    # 创建数据库连接
    engine = create_engine("mysql+pymysql://root:root@localhost:3306/stock_cursor")
    
    # 创建分析器实例
    analyzer = FactorCorrelationAnalyzer(engine)

    # 1. 加载数据
    data = analyzer.load_data(
        start_date='2023-01-01',
        end_date='2023-12-31',
        stock_codes=['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']
    )
    
    # 2. 计算101个因子
    factors = analyzer.get_all_alpha_factors(data)
    
    # 3. 相关性分析
    result = analyzer.analyze_101_factors(data)
    
    # 4. 🔥 重点：筛选出低相关性的优质因子组合
    selected_factors = analyzer.identify_redundant_factors(
        factors,
        max_factors=20,  # 从101个中选择20个
        correlation_threshold=0.5
    )
    
    # 5. 对选中的因子进行深度分析
    final_report = analyzer.comprehensive_correlation_report(selected_factors)
    
    return final_report
'''
# ✅ 完整的使用流程
def main_analysis():
    
        批量计算: 使用循环或反射机制计算所有101个alpha因子
        性能优化: 分批处理避免内存问题
        阈值调整: 提高相关性阈值（0.7-0.8）筛选真正高相关的因子
        去重复: 识别冗余因子组，避免信息重复
        因子选择: 从101个中选择低相关性的优质因子组合
        分层分析: 先整体分析，再聚焦到筛选后的因子子集

        这样您就能高效地对101个alpha因子进行正相关性分析，并找出最有价值的因子组合！
    
    # 创建数据库连接
    engine = create_engine("mysql+pymysql://root:root@localhost:3306/stock_cursor")
    
    # 创建分析器实例
    analyzer = FactorCorrelationAnalyzer(engine)
    
    # 1. 加载数据
    print("\n📊 步骤1: 加载数据")
    data = analyzer.load_data(
        start_date='2023-01-01',
        end_date='2023-12-31',
        stock_codes=['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']
    )
    
    # 2. 计算Alpha因子
    print("\n🧮 步骤2: 计算Alpha因子")
    factors = analyzer.get_all_alpha_factors(data)
    
    if not factors:
        print("❌ 没有成功计算出任何因子，请检查数据和代码")
        return None
    
    # 3. 生成筛选报告
    print("\n📋 步骤3: 生成因子筛选报告")
    report = analyzer.generate_factor_selection_report(factors, data)
    
    # 4. 绘制相关性热力图
    print("\n📈 步骤4: 生成可视化图表")
    if len(factors) <= 20:  # 如果因子数量不多，直接绘制
        analyzer.plot_correlation_heatmap(factors, figsize=(12, 10))
    else:
        # 如果因子太多，只绘制筛选后的因子
        selected_factors = report['selected_factors']
        analyzer.plot_correlation_heatmap(selected_factors, figsize=(12, 10))
    
    print("\n🎉 分析完成！")
    return report

# 使用示例
def example_usage():
    """使用示例"""
    # 创建数据库连接
    engine = create_engine("mysql+pymysql://root:root@localhost:3306/stock_cursor")
    
    # 创建分析器实例
    analyzer = FactorCorrelationAnalyzer(engine)
    
    # 加载数据
    data = analyzer.load_data(
        start_date='2023-01-01',
        end_date='2023-12-31',
        stock_codes=['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']
    )
    
    # 计算示例因子
    factors = {
        'momentum': analyzer.momentum_factor(data),
        'volatility': analyzer.volatility_factor(data),
        'turnover': analyzer.turnover_factor(data),
        'size': analyzer.size_factor(data),
        'value': analyzer.value_factor(data)
    }
    
    # 生成综合分析报告
    report = analyzer.comprehensive_correlation_report(factors)
    
    # 绘制相关性热力图
    analyzer.plot_correlation_heatmap(factors)
    
    print("\n因子正相关性分析器已准备就绪！")
    print("支持的核心功能:")
    print("✓ 因子间相关性矩阵计算")
    print("✓ 因子与收益相关性分析")
    print("✓ 滚动相关性分析")
    print("✓ 截面相关性分析")
    print("✓ 正相关性专项分析")
    print("✓ 因子稳定性分析")
    print("✓ 综合相关性报告")
    print("✓ 相关性热力图可视化")

# ✅ 测试函数
def test_fixed_factors():
    """测试修复后的因子计算"""
    from sqlalchemy import create_engine
    
    # 创建数据库连接
    engine = create_engine("mysql+pymysql://root:root@localhost:3306/stock_cursor")
    
    # 创建分析器实例
    analyzer = FactorCorrelationAnalyzer(engine)
    
    # 加载少量数据进行测试
    data = analyzer.load_data(
        start_date='2023-01-01',
        end_date='2023-01-31',  # 只加载一个月的数据
        stock_codes=['000001.SZ', '000002.SZ']  # 只选择两只股票
    )
    
    print("开始测试修复后的因子计算...")
    
    # 测试几个有问题的因子
    test_factors = ['alpha001', 'alpha010', 'alpha083', 'alpha097']
    
    for factor_name in test_factors:
        try:
            if hasattr(analyzer, factor_name):
                print(f"测试 {factor_name}...")
                method = getattr(analyzer, factor_name)
                
                # 使用改进的参数映射
                import inspect
                sig = inspect.signature(method)
                params = [p for p in sig.parameters.keys() if p != 'self']
                
                # 准备参数
                kwargs = {}
                for param in params:
                    if param in data:
                        kwargs[param] = data[param]
                    elif param == 'open':
                        kwargs[param] = data['open']
                    elif param == 'adv20':
                        kwargs[param] = analyzer.ts_mean(data['volume'], 20)
                    elif param == 'adv81':
                        kwargs[param] = analyzer.ts_mean(data['volume'], 81)
                
                if len(kwargs) == len(params):
                    result = method(**kwargs)
                    print(f"✓ {factor_name} 计算成功，结果形状: {result.shape}")
                else:
                    print(f"⚠️ {factor_name} 参数不完整")
                    
        except Exception as e:
            print(f"✗ {factor_name} 测试失败: {e}")
    
    print("\n测试完成！")
'''
if __name__ == "__main__":
    main_analysis()