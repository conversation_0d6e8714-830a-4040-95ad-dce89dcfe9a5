# 数据库相关
pymysql==1.1.0
mysql-connector-python==8.2.0
SQLAlchemy==2.0.23
redis==5.0.1

# 数据处理
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.9.0

# 回测框架
backtrader==**********

# 参数优化
optuna==3.4.0
deap>=1.3.0

# 调度和监控
APScheduler==3.10.4
psutil>=5.9.0

# Web框架和API
flask>=2.3.0
flask-cors>=4.0.0
flask-restful>=0.3.10
flask-socketio>=5.3.0
python-socketio==5.10.0
eventlet==0.33.3
gunicorn==21.2.0

# WebSocket客户端
websocket-client==1.6.4
websockets>=11.0.0

# 技术指标
# TA-Lib==0.4.28
talib-binary==0.4.19

# 可视化
matplotlib>=3.5.0
plotly==5.17.0
seaborn>=0.11.0

# Excel处理
openpyxl==3.1.2

# 日志和配置
loguru>=0.7.0
python-dotenv==1.0.0
pyyaml>=6.0

# 工具库
tqdm>=4.64.0
click>=8.1.0
requests==2.31.0
urllib3==2.1.0
tabulate==0.9.0

# 测试
pytest>=7.0.0
pytest-cov==4.1.0
pytest-asyncio>=0.21.0

# 开发工具
black>=22.0.0
flake8>=5.0.0
isort>=5.10.0

# 数据获取
akshare>=1.8.0
tushare>=1.2.0
yfinance>=0.1.70

# 技术分析
ta>=0.10.0

# 机器学习
scikit-learn>=1.1.0
xgboost>=1.6.0
lightgbm>=3.3.0

# 异步处理
asyncio>=3.4.3

# 数据验证
pydantic>=1.10.0

# 并发处理
concurrent-futures>=3.1.1

# 文档生成
sphinx>=5.0.0
sphinx-rtd-theme>=1.2.0

# 可选：量化金融库
# quantlib>=1.29
# zipline>=2.2.0
# backtrader>=1.9.76

# 可选：机器学习
# xgboost>=1.6.0
# lightgbm>=3.3.0
# catboost>=1.1.0

# 可选：深度学习
# torch>=1.12.0
# tensorflow>=2.9.0

# 可选：时间序列预测
# prophet>=1.1.0
# statsmodels>=0.13.0

# 可选：优化算法
# optuna>=3.0.0
# hyperopt>=0.2.7

# 可选：分布式计算
# dask>=2022.8.0
# ray>=2.0.0 